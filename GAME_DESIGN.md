# ATC Game - Detailed Game Design Document

## Core Gameplay Loop
1. **Aircraft Spawn:** New aircraft appear at map edges or gates
2. **Assessment:** Player evaluates traffic situation and conflicts
3. **Command:** Player issues commands (heading, altitude, speed)
4. **Monitor:** Watch aircraft execute commands and adjust as needed
5. **Resolution:** Aircraft safely land/takeoff or conflicts occur
6. **Scoring:** Points awarded/deducted based on performance

## Aircraft Types and Specifications

### Small Aircraft (Cessna-type)
- **Speed:** 80-120 knots
- **Climb Rate:** 500 ft/min
- **Turn Rate:** Fast (3°/second)
- **Wake Turbulence:** None
- **Separation Required:** 1 mile

### Regional Jets (CRJ-type)
- **Speed:** 200-300 knots
- **Climb Rate:** 1500 ft/min
- **Turn Rate:** Medium (2°/second)
- **Wake Turbulence:** Light
- **Separation Required:** 3 miles

### Commercial Jets (737/A320-type)
- **Speed:** 250-450 knots
- **Climb Rate:** 2000 ft/min
- **Turn Rate:** Slow (1.5°/second)
- **Wake Turbulence:** Medium
- **Separation Required:** 5 miles

### Wide-body Jets (777/A330-type)
- **Speed:** 250-500 knots
- **Climb Rate:** 1800 ft/min
- **Turn Rate:** Very Slow (1°/second)
- **Wake Turbulence:** Heavy
- **Separation Required:** 6 miles

### Cargo Aircraft (747F-type)
- **Speed:** 200-450 knots
- **Climb Rate:** 1200 ft/min
- **Turn Rate:** Very Slow (0.8°/second)
- **Wake Turbulence:** Heavy
- **Separation Required:** 6 miles

## Command System

### Basic Commands
- **Heading:** "Turn left/right to heading XXX"
- **Altitude:** "Climb/descend to XXXX feet"
- **Speed:** "Reduce/increase speed to XXX knots"
- **Hold:** "Enter holding pattern at current position"

### Advanced Commands
- **Direct To:** "Proceed direct to waypoint XXX"
- **Intercept:** "Turn to intercept localizer/glideslope"
- **Contact:** "Contact tower on frequency XXX.X"
- **Expedite:** "Expedite climb/descent"

### Emergency Commands
- **Priority:** "Declare emergency, priority handling"
- **Vector:** "Vector for immediate landing"
- **Clear:** "Clear all traffic, emergency approach"

## Scoring System

### Points Awarded
- **Safe Landing:** +100 points
- **On-time Departure:** +50 points
- **Efficient Routing:** +25 points
- **Smooth Traffic Flow:** +10 points/minute
- **Emergency Handling:** +200 points

### Penalties
- **Near Miss (< 1 mile):** -50 points
- **Collision:** -500 points (game over)
- **Excessive Delay:** -10 points/minute
- **Runway Incursion:** -100 points
- **Lost Communication:** -25 points

### Multipliers
- **Weather Conditions:** 1.5x in poor weather
- **Traffic Volume:** 1.2x for high traffic
- **Airport Complexity:** 1.3x for complex airports

## Airport Layouts

### Starter Airport (Regional)
- **Runways:** 1 (5000ft)
- **Gates:** 6
- **Traffic:** 5-10 aircraft/hour
- **Complexity:** Low

### Medium Airport (Commercial)
- **Runways:** 2 intersecting
- **Gates:** 20
- **Traffic:** 15-25 aircraft/hour
- **Complexity:** Medium

### Major Hub (International)
- **Runways:** 4 parallel
- **Gates:** 50+
- **Traffic:** 30-50 aircraft/hour
- **Complexity:** High

## Weather Effects

### Clear Conditions
- **Visibility:** Unlimited
- **Wind:** 0-10 knots
- **Impact:** None

### Light Rain
- **Visibility:** 5 miles
- **Wind:** 5-15 knots
- **Impact:** Slightly reduced approach speeds

### Heavy Rain/Fog
- **Visibility:** 1-3 miles
- **Wind:** 10-25 knots
- **Impact:** ILS approaches only, reduced capacity

### Thunderstorms
- **Visibility:** 0.5-2 miles
- **Wind:** 15-40 knots (gusts)
- **Impact:** Runway closures, holding patterns

## Emergency Scenarios

### Engine Failure
- **Frequency:** 1 in 100 flights
- **Action Required:** Priority landing clearance
- **Time Pressure:** 10-15 minutes

### Fuel Emergency
- **Frequency:** 1 in 200 flights
- **Action Required:** Immediate approach
- **Time Pressure:** 5-10 minutes

### Medical Emergency
- **Frequency:** 1 in 150 flights
- **Action Required:** Priority handling
- **Time Pressure:** Variable

### Communication Failure
- **Frequency:** 1 in 300 flights
- **Action Required:** Light gun signals
- **Time Pressure:** Until communication restored

## Difficulty Progression

### Beginner Level
- **Traffic:** 3-5 aircraft
- **Weather:** Clear only
- **Emergencies:** None
- **Assistance:** Full guidance

### Intermediate Level
- **Traffic:** 8-12 aircraft
- **Weather:** Light rain/wind
- **Emergencies:** Rare
- **Assistance:** Minimal guidance

### Expert Level
- **Traffic:** 15-25 aircraft
- **Weather:** All conditions
- **Emergencies:** Regular
- **Assistance:** None

### Master Level
- **Traffic:** 25+ aircraft
- **Weather:** Severe conditions
- **Emergencies:** Frequent
- **Assistance:** None
- **Special:** Equipment failures

## User Interface Design

### Radar Display
- **Scale:** Adjustable (5-50 mile radius)
- **Aircraft Icons:** Different shapes per type
- **Data Tags:** Callsign, altitude, speed
- **Weather Overlay:** Precipitation/wind

### Command Panel
- **Selected Aircraft:** Highlighted info
- **Command Buttons:** Quick access
- **Text Input:** Direct command entry
- **History:** Recent commands

### Status Panel
- **Score:** Current points
- **Traffic Count:** Active aircraft
- **Weather:** Current conditions
- **Alerts:** System warnings

This design provides a solid foundation for implementing the core gameplay mechanics while maintaining scalability for future features.
