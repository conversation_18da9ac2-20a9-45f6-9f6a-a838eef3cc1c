#!/usr/bin/env python3
"""
Airport Data Fetcher
Fetches real airport data from OpenAIP and OpenStreetMap for ATC game integration.
"""

import json
import os
import requests
import overpy
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class Airport:
    """Airport data structure"""
    icao: str
    name: str
    latitude: float
    longitude: float
    elevation: int
    runways: List[Dict]
    taxiways: List[Dict]
    gates: List[Dict]
    navaids: List[Dict]


class AirportDataFetcher:
    """Fetches and processes real airport data from multiple sources"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.cache_dir = self.data_dir / "cache"
        self.cache_dir.mkdir(exist_ok=True)
        
        # OpenAIP API base URL
        self.openaip_base = "https://api.openaip.net/api"
        
        # Overpass API for OpenStreetMap
        self.overpass_api = overpy.Overpass()
    
    def fetch_openaip_airport(self, icao_code: str) -> Optional[Dict]:
        """Fetch airport data from OpenAIP"""
        cache_file = self.cache_dir / f"openaip_{icao_code}.json"
        
        # Check cache first
        if cache_file.exists():
            with open(cache_file, 'r') as f:
                return json.load(f)
        
        try:
            # Fetch from API
            url = f"{self.openaip_base}/airports"
            params = {"icao": icao_code}
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Cache the response
            with open(cache_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            return data
            
        except requests.RequestException as e:
            print(f"Error fetching OpenAIP data for {icao_code}: {e}")
            return None
    
    def fetch_osm_airport_data(self, lat: float, lon: float, radius: float = 0.02) -> Dict:
        """Fetch airport layout data from OpenStreetMap"""
        cache_file = self.cache_dir / f"osm_{lat:.4f}_{lon:.4f}.json"
        
        # Check cache first
        if cache_file.exists():
            with open(cache_file, 'r') as f:
                return json.load(f)
        
        try:
            # Build Overpass query for airport features
            query = f"""
            [out:json][timeout:25];
            (
              way["aeroway"="runway"]({lat-radius},{lon-radius},{lat+radius},{lon+radius});
              way["aeroway"="taxiway"]({lat-radius},{lon-radius},{lat+radius},{lon+radius});
              way["aeroway"="terminal"]({lat-radius},{lon-radius},{lat+radius},{lon+radius});
              way["aeroway"="gate"]({lat-radius},{lon-radius},{lat+radius},{lon+radius});
              way["aeroway"="apron"]({lat-radius},{lon-radius},{lat+radius},{lon+radius});
            );
            out geom;
            """
            
            result = self.overpass_api.query(query)
            
            # Process the result into a serializable format
            data = {
                "runways": [],
                "taxiways": [],
                "terminals": [],
                "gates": [],
                "aprons": []
            }
            
            for way in result.ways:
                way_data = {
                    "id": way.id,
                    "tags": way.tags,
                    "coordinates": [[float(node.lat), float(node.lon)] for node in way.nodes]
                }
                
                aeroway_type = way.tags.get("aeroway", "unknown")
                if aeroway_type in data:
                    data[aeroway_type + "s"].append(way_data)
            
            # Cache the processed data
            with open(cache_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            return data
            
        except Exception as e:
            print(f"Error fetching OSM data for {lat}, {lon}: {e}")
            return {"runways": [], "taxiways": [], "terminals": [], "gates": [], "aprons": []}
    
    def process_airport_data(self, icao_code: str) -> Optional[Airport]:
        """Process and combine data from multiple sources"""
        print(f"Processing airport data for {icao_code}...")
        
        # Fetch OpenAIP data
        openaip_data = self.fetch_openaip_airport(icao_code)
        if not openaip_data or not openaip_data.get("items"):
            print(f"No OpenAIP data found for {icao_code}")
            return None
        
        airport_info = openaip_data["items"][0]  # Take first result
        
        # Extract basic airport information
        lat = float(airport_info["geometry"]["coordinates"][1])
        lon = float(airport_info["geometry"]["coordinates"][0])
        
        # Fetch detailed layout from OpenStreetMap
        osm_data = self.fetch_osm_airport_data(lat, lon)
        
        # Create Airport object
        airport = Airport(
            icao=icao_code,
            name=airport_info["properties"]["name"],
            latitude=lat,
            longitude=lon,
            elevation=airport_info["properties"].get("elevation", 0),
            runways=osm_data.get("runways", []),
            taxiways=osm_data.get("taxiways", []),
            gates=osm_data.get("gates", []),
            navaids=[]  # TODO: Fetch navaid data
        )
        
        return airport
    
    def save_airport_data(self, airport: Airport) -> None:
        """Save processed airport data to files"""
        airport_dir = self.data_dir / "airports" / airport.icao
        airport_dir.mkdir(parents=True, exist_ok=True)
        
        # Save metadata
        metadata = {
            "icao": airport.icao,
            "name": airport.name,
            "latitude": airport.latitude,
            "longitude": airport.longitude,
            "elevation": airport.elevation
        }
        
        with open(airport_dir / "metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Save runway data
        with open(airport_dir / "runways.json", 'w') as f:
            json.dump(airport.runways, f, indent=2)
        
        # Save taxiway data
        with open(airport_dir / "taxiways.json", 'w') as f:
            json.dump(airport.taxiways, f, indent=2)
        
        # Save gate data
        with open(airport_dir / "gates.json", 'w') as f:
            json.dump(airport.gates, f, indent=2)
        
        print(f"Airport data saved to {airport_dir}")
    
    def fetch_multiple_airports(self, icao_codes: List[str]) -> List[Airport]:
        """Fetch data for multiple airports"""
        airports = []
        
        for icao in icao_codes:
            airport = self.process_airport_data(icao)
            if airport:
                self.save_airport_data(airport)
                airports.append(airport)
            else:
                print(f"Failed to process {icao}")
        
        return airports


def main():
    """Example usage of the airport data fetcher"""
    fetcher = AirportDataFetcher()
    
    # List of starter airports for the game
    starter_airports = [
        "KPAO",  # Palo Alto - Small regional
        "KSJC",  # San Jose - Medium commercial
        "KSFO",  # San Francisco - Major hub
    ]
    
    print("Fetching airport data...")
    airports = fetcher.fetch_multiple_airports(starter_airports)
    
    print(f"\nSuccessfully processed {len(airports)} airports:")
    for airport in airports:
        print(f"  {airport.icao}: {airport.name}")
        print(f"    Runways: {len(airport.runways)}")
        print(f"    Taxiways: {len(airport.taxiways)}")
        print(f"    Gates: {len(airport.gates)}")


if __name__ == "__main__":
    main()
