#!/usr/bin/env python3
"""
Satellite Imagery Fetcher
Downloads satellite imagery from Google Maps Static API for airport backgrounds.
"""

import os
import requests
from PIL import Image
from io import BytesIO
from pathlib import Path
from typing import Tuple, Optional


class SatelliteImageryFetcher:
    """Fetches satellite imagery for airport backgrounds"""
    
    def __init__(self, api_key: str, data_dir: str = "data"):
        self.api_key = api_key
        self.data_dir = Path(data_dir)
        self.images_dir = self.data_dir / "images"
        self.images_dir.mkdir(parents=True, exist_ok=True)
        
        # Google Maps Static API base URL
        self.base_url = "https://maps.googleapis.com/maps/api/staticmap"
    
    def fetch_satellite_image(
        self, 
        lat: float, 
        lon: float, 
        zoom: int = 15, 
        size: Tuple[int, int] = (1024, 1024),
        map_type: str = "satellite"
    ) -> Optional[Image.Image]:
        """
        Fetch satellite image from Google Maps Static API
        
        Args:
            lat: Latitude of center point
            lon: Longitude of center point
            zoom: Zoom level (1-20)
            size: Image size in pixels (max 640x640 for free tier)
            map_type: Map type ('satellite', 'hybrid', 'roadmap', 'terrain')
        
        Returns:
            PIL Image object or None if failed
        """
        
        params = {
            'center': f"{lat},{lon}",
            'zoom': zoom,
            'size': f"{size[0]}x{size[1]}",
            'maptype': map_type,
            'key': self.api_key,
            'format': 'png'
        }
        
        try:
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Check if response is an image
            if response.headers.get('content-type', '').startswith('image'):
                return Image.open(BytesIO(response.content))
            else:
                print(f"Error: {response.text}")
                return None
                
        except requests.RequestException as e:
            print(f"Error fetching satellite image: {e}")
            return None
    
    def fetch_airport_imagery(
        self, 
        icao_code: str, 
        lat: float, 
        lon: float,
        zoom_levels: list = [13, 15, 17]
    ) -> bool:
        """
        Fetch multiple zoom levels of satellite imagery for an airport
        
        Args:
            icao_code: Airport ICAO code
            lat: Airport latitude
            lon: Airport longitude
            zoom_levels: List of zoom levels to fetch
        
        Returns:
            True if successful, False otherwise
        """
        
        airport_dir = self.images_dir / icao_code
        airport_dir.mkdir(exist_ok=True)
        
        success = True
        
        for zoom in zoom_levels:
            print(f"Fetching {icao_code} satellite image at zoom {zoom}...")
            
            # Fetch satellite image
            image = self.fetch_satellite_image(lat, lon, zoom)
            if image:
                filename = airport_dir / f"satellite_zoom_{zoom}.png"
                image.save(filename)
                print(f"  Saved: {filename}")
            else:
                print(f"  Failed to fetch zoom level {zoom}")
                success = False
            
            # Also fetch hybrid (satellite + labels) version
            hybrid_image = self.fetch_satellite_image(lat, lon, zoom, map_type="hybrid")
            if hybrid_image:
                filename = airport_dir / f"hybrid_zoom_{zoom}.png"
                hybrid_image.save(filename)
                print(f"  Saved: {filename}")
        
        return success
    
    def create_tiled_image(
        self, 
        lat: float, 
        lon: float, 
        zoom: int = 15,
        tile_size: int = 640,
        grid_size: Tuple[int, int] = (2, 2)
    ) -> Optional[Image.Image]:
        """
        Create a larger image by fetching multiple tiles and stitching them together
        
        Args:
            lat: Center latitude
            lon: Center longitude
            zoom: Zoom level
            tile_size: Size of each tile (max 640 for free tier)
            grid_size: Number of tiles in (width, height)
        
        Returns:
            Stitched PIL Image or None if failed
        """
        
        # Calculate offset for each tile
        # This is a simplified calculation - real implementation would need
        # proper map projection calculations
        lat_offset = 0.01 * (2 ** (15 - zoom))  # Rough approximation
        lon_offset = 0.01 * (2 ** (15 - zoom))
        
        tiles = []
        
        for row in range(grid_size[1]):
            tile_row = []
            for col in range(grid_size[0]):
                # Calculate tile center
                tile_lat = lat + (row - grid_size[1]/2 + 0.5) * lat_offset
                tile_lon = lon + (col - grid_size[0]/2 + 0.5) * lon_offset
                
                # Fetch tile
                tile = self.fetch_satellite_image(
                    tile_lat, tile_lon, zoom, (tile_size, tile_size)
                )
                
                if tile:
                    tile_row.append(tile)
                else:
                    print(f"Failed to fetch tile at {tile_lat}, {tile_lon}")
                    return None
            
            tiles.append(tile_row)
        
        # Stitch tiles together
        total_width = grid_size[0] * tile_size
        total_height = grid_size[1] * tile_size
        
        stitched = Image.new('RGB', (total_width, total_height))
        
        for row in range(grid_size[1]):
            for col in range(grid_size[0]):
                x = col * tile_size
                y = row * tile_size
                stitched.paste(tiles[row][col], (x, y))
        
        return stitched


def main():
    """Example usage - requires Google Maps API key"""
    
    # You need to get a Google Maps API key from:
    # https://developers.google.com/maps/documentation/maps-static/get-api-key
    api_key = os.getenv("GOOGLE_MAPS_API_KEY")
    
    if not api_key:
        print("Please set GOOGLE_MAPS_API_KEY environment variable")
        print("Get your API key from: https://developers.google.com/maps/documentation/maps-static/get-api-key")
        return
    
    fetcher = SatelliteImageryFetcher(api_key)
    
    # Example airports with coordinates
    airports = [
        ("KPAO", 37.461111, -122.115000),  # Palo Alto
        ("KSJC", 37.362598, -121.929001),  # San Jose
        ("KSFO", 37.621311, -122.378968),  # San Francisco
    ]
    
    for icao, lat, lon in airports:
        print(f"\nFetching imagery for {icao}...")
        success = fetcher.fetch_airport_imagery(icao, lat, lon)
        if success:
            print(f"Successfully fetched imagery for {icao}")
        else:
            print(f"Failed to fetch some imagery for {icao}")


if __name__ == "__main__":
    main()
