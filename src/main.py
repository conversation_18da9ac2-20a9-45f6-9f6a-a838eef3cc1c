#!/usr/bin/env python3
"""
ATC Game - Main Entry Point
Semi-realistic Air Traffic Control simulation game using real airport data.
"""

import pygame
import sys
import os
from pathlib import Path

# Add src directory to path for imports
sys.path.append(str(Path(__file__).parent))

from core.game_engine import GameEngine
from core.config import GameConfig


def main():
    """Main entry point for the ATC game"""
    
    # Initialize Pygame
    pygame.init()
    
    try:
        # Create game configuration
        config = GameConfig()
        
        # Create and run the game engine
        game = GameEngine(config)
        game.run()
        
    except Exception as e:
        print(f"Error starting game: {e}")
        return 1
    
    finally:
        # Clean shutdown
        pygame.quit()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
