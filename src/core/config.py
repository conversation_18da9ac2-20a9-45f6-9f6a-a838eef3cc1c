"""
Game Configuration
Central configuration management for the ATC game.
"""

import os
from pathlib import Path
from dataclasses import dataclass
from typing import Tuple


@dataclass
class GameConfig:
    """Main game configuration settings"""
    
    # Display settings
    SCREEN_WIDTH: int = 1400
    SCREEN_HEIGHT: int = 900
    FPS: int = 60
    FULLSCREEN: bool = False
    
    # Game settings
    GAME_TITLE: str = "ATC Game - Air Traffic Control Simulator"
    VERSION: str = "0.1.0"
    
    # Radar settings
    RADAR_CENTER_X: int = 700  # Center of radar display
    RADAR_CENTER_Y: int = 450
    RADAR_RADIUS: int = 350
    RADAR_RANGE_NM: float = 50.0  # Nautical miles
    RADAR_ZOOM_MIN: float = 0.5
    RADAR_ZOOM_MAX: float = 5.0
    RADAR_ZOOM_DEFAULT: float = 1.0
    
    # Aircraft settings
    AIRCRAFT_ICON_SIZE: int = 8
    AIRCRAFT_TRAIL_LENGTH: int = 10
    AIRCRAFT_UPDATE_RATE: float = 1.0  # Updates per second
    
    # Colors (RGB tuples)
    COLOR_BACKGROUND: Tuple[int, int, int] = (20, 25, 30)
    COLOR_RADAR_GRID: Tuple[int, int, int] = (0, 100, 0)
    COLOR_RADAR_SWEEP: Tuple[int, int, int] = (0, 255, 0)
    COLOR_AIRCRAFT: Tuple[int, int, int] = (255, 255, 0)
    COLOR_AIRCRAFT_SELECTED: Tuple[int, int, int] = (255, 100, 100)
    COLOR_RUNWAY: Tuple[int, int, int] = (150, 150, 150)
    COLOR_TAXIWAY: Tuple[int, int, int] = (100, 100, 100)
    COLOR_TEXT: Tuple[int, int, int] = (255, 255, 255)
    COLOR_WARNING: Tuple[int, int, int] = (255, 0, 0)
    COLOR_SUCCESS: Tuple[int, int, int] = (0, 255, 0)
    
    # File paths
    PROJECT_ROOT: Path = Path(__file__).parent.parent.parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    ASSETS_DIR: Path = PROJECT_ROOT / "assets"
    AIRPORTS_DIR: Path = DATA_DIR / "airports"
    CACHE_DIR: Path = DATA_DIR / "cache"
    
    # API settings
    GOOGLE_MAPS_API_KEY: str = os.getenv("GOOGLE_MAPS_API_KEY", "")
    OPENAIP_BASE_URL: str = "https://api.openaip.net/api"
    
    # Physics settings
    NAUTICAL_MILE_TO_PIXELS: float = 10.0  # Pixels per nautical mile at default zoom
    KNOTS_TO_PIXELS_PER_SECOND: float = 0.1  # Conversion factor for aircraft speed
    
    # Collision detection
    SEPARATION_MINIMUM_NM: float = 3.0  # Minimum separation in nautical miles
    SEPARATION_WARNING_NM: float = 5.0  # Warning threshold
    
    # Default airport
    DEFAULT_AIRPORT: str = "KPAO"  # Palo Alto Airport
    
    def __post_init__(self):
        """Create necessary directories"""
        self.DATA_DIR.mkdir(exist_ok=True)
        self.ASSETS_DIR.mkdir(exist_ok=True)
        self.AIRPORTS_DIR.mkdir(exist_ok=True)
        self.CACHE_DIR.mkdir(exist_ok=True)
    
    @property
    def screen_size(self) -> Tuple[int, int]:
        """Get screen size as tuple"""
        return (self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
    
    @property
    def radar_center(self) -> Tuple[int, int]:
        """Get radar center as tuple"""
        return (self.RADAR_CENTER_X, self.RADAR_CENTER_Y)
    
    def pixels_to_nautical_miles(self, pixels: float, zoom: float = 1.0) -> float:
        """Convert pixels to nautical miles"""
        return pixels / (self.NAUTICAL_MILE_TO_PIXELS * zoom)
    
    def nautical_miles_to_pixels(self, nm: float, zoom: float = 1.0) -> float:
        """Convert nautical miles to pixels"""
        return nm * self.NAUTICAL_MILE_TO_PIXELS * zoom
