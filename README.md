# Semi-Realistic Air Traffic Control Game

A challenging and engaging air traffic control simulation that balances realism with accessibility.

## Quick Start

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Installation
```bash
# Clone or download the project
cd ATC-Game

# Install dependencies
pip install -r requirements.txt

# Set up Google Maps API key (optional, for satellite imagery)
export GOOGLE_MAPS_API_KEY="your_api_key_here"

# Fetch real airport data (optional)
python tools/airport_data_fetcher.py

# Run the game
python src/main.py
```

## Development Status

This project is currently in active development. See [TASK_LIST.md](TASK_LIST.md) for detailed progress tracking.

### Current Phase: Phase 1 - Game Design & Planning
- [x] Project structure setup
- [x] Core design documentation
- [ ] Technology stack finalization
- [ ] Development environment setup

## Project Structure

```
ATC-Game/
├── docs/                 # Documentation
├── src/                  # Source code
│   ├── core/            # Core game systems
│   ├── ui/              # User interface
│   ├── systems/         # Game systems
│   └── data/            # Game data
├── assets/              # Game assets
├── tests/               # Unit tests
└── tools/               # Development tools
```

## Features (Planned)

### Core Gameplay
- ✅ **Real airport data integration** using OpenAIP and OpenStreetMap
- ✅ **Satellite imagery backgrounds** from Google Maps
- ✅ Realistic aircraft behavior and physics
- ✅ Multiple aircraft types with unique characteristics
- ✅ Dynamic weather effects
- ✅ Emergency scenarios and special events
- ✅ Progressive difficulty through real-world airports

### Game Modes
- **Career Mode:** Progress through increasingly complex airports
- **Endless Mode:** Continuous play with scaling difficulty
- **Tutorial Mode:** Learn ATC basics and advanced techniques

### Realism Levels
- **Simplified Mode:** Casual gameplay with reduced complexity
- **Full Simulation:** Realistic procedures and communications

## Technology Stack

- **Engine:** Python + Pygame
- **Graphics:** 2D radar-style interface
- **Audio:** Pygame mixer for radio chatter and alerts
- **Testing:** pytest for unit testing

## Contributing

This is currently a solo development project. The task list and documentation are designed to support rapid development over a 7-week timeline.

## Development Timeline

- **Week 1:** Game Design & Planning
- **Week 2:** Prototype Radar & Basic Controls
- **Week 3:** First Airport & Core Traffic Flow
- **Week 4:** Weather & Event System
- **Week 5:** Career Mode & Scoring
- **Week 6:** Polish & Optimization
- **Week 7:** Testing & Launch

## License

[To be determined]

## Contact

[Your contact information]

---

*This project aims to create an engaging ATC simulation that teaches real air traffic control concepts while providing entertaining gameplay for aviation enthusiasts and casual gamers alike.*
